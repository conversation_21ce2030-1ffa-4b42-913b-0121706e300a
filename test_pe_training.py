# test_pe_training.py
# 测试PE集成后的训练流程
import os
import torch
import torch.nn as nn
import pandas as pd
from model import MVGNN
from utils import TaskDataset, Seed_everything
from torch.utils.data import DataLoader
from focalLoss import FocalLoss
from tqdm import tqdm

def test_training_with_pe():
    """测试集成PE后的训练流程"""
    print("开始测试PE集成后的训练流程...")
    
    # 设置随机种子
    Seed_everything(2024)
    
    # 检查GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    try:
        train_df = pd.read_csv('./datasets/PRO_Train335.csv')
        ID_list = list(set(train_df['ID']))[:10]  # 只用前10个进行快速测试
        
        all_protein_data = {}
        valid_ids = []
        
        for pdb_id in ID_list:
            try:
                # 检查所有必需的文件是否存在
                required_files = [
                    f'./feature/{pdb_id}_X.tensor',
                    f'./feature/{pdb_id}_node_feature.tensor',
                    f'./feature/{pdb_id}_mask.tensor',
                    f'./feature/{pdb_id}_label.tensor',
                    f'./feature/{pdb_id}_adj.tensor',
                    f'./feature/{pdb_id}_pe.tensor'  # 确保PE文件存在
                ]
                
                if all(os.path.exists(f) for f in required_files):
                    all_protein_data[pdb_id] = (
                        torch.load(f'./feature/{pdb_id}_X.tensor'),
                        torch.load(f'./feature/{pdb_id}_node_feature.tensor'),
                        torch.load(f'./feature/{pdb_id}_mask.tensor'),
                        torch.load(f'./feature/{pdb_id}_label.tensor'),
                        torch.load(f'./feature/{pdb_id}_adj.tensor')
                    )
                    valid_ids.append(pdb_id)
                    
            except Exception as e:
                print(f"跳过 {pdb_id}: {e}")
                continue
        
        print(f"✓ 成功加载 {len(all_protein_data)} 个蛋白质数据")
        
        # 创建数据集
        valid_train_df = train_df[train_df['ID'].isin(valid_ids)].reset_index(drop=True)
        train_dataset = TaskDataset(valid_train_df, all_protein_data, ['label'])
        train_dataloader = DataLoader(
            train_dataset, 
            batch_size=2, 
            collate_fn=train_dataset.collate_fn, 
            shuffle=True
        )
        
        print("✓ 数据集创建成功")
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False
    
    # 初始化模型
    try:
        model = MVGNN(
            node_features=1024 + 14 + 20,  # ProtTrans + DSSP + BLOSUM62
            edge_features=16,
            hidden_dim=128,
            num_encoder_layers=4,
            k_neighbors=30,
            augment_eps=0.1,
            dropout=0.3
        ).to(device)
        
        print("✓ 模型初始化成功")
        
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        return False
    
    # 设置优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    criterion = FocalLoss(alpha=1, gamma=2)
    
    # 训练几个步骤
    try:
        model.train()
        total_loss = 0
        num_batches = 0
        
        print("开始训练测试...")
        for batch_idx, data in enumerate(tqdm(train_dataloader, desc="训练")):
            pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到GPU
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            pe = pe.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
            
            # 计算损失
            masked_outputs = torch.masked_select(outputs, protein_masks.bool())
            masked_labels = torch.masked_select(labels, protein_masks.bool())
            
            loss = criterion(masked_outputs, masked_labels)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 只训练3个批次进行测试
            if batch_idx >= 2:
                break
        
        avg_loss = total_loss / num_batches
        print(f"✓ 训练测试成功，平均损失: {avg_loss:.4f}")
        
    except Exception as e:
        print(f"✗ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("🎉 PE集成训练测试全部通过！")
    return True

if __name__ == "__main__":
    test_training_with_pe()
