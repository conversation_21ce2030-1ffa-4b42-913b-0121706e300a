# 阶段二：模型架构升级实验方案

## 🎯 实验目标
用GAT替换GCN，验证注意力机制是否能提升蛋白质结合位点预测性能

## 📋 实验设计

### 实验1：单层GAT替换 (保守方案)
**目标**: 最小化风险，验证GAT的基本效果
**方法**: 只替换第一个GCN层 (gcn1)

```python
# 在 self_attention.py 的 TransformerLayer.__init__ 中
self.gcn1 = GCN(128)  # 替换这个
self.gcn2 = GCN(128)  # 保留这个

# 替换为
from gat_layer_improved import MultiHeadGATLayer
self.gat1 = MultiHeadGATLayer(128, 128, n_heads=4, dropout=0.1)
self.gcn2 = GCN(128)  # 保留
```

### 实验2：双层GAT替换 (激进方案)
**目标**: 如果实验1成功，进一步验证全GAT的效果
**方法**: 替换两个GCN层

```python
# 替换两个层
self.gat1 = MultiHeadGATLayer(128, 128, n_heads=4, dropout=0.1)
self.gat2 = MultiHeadGATLayer(128, 128, n_heads=4, dropout=0.1)
```

### 实验3：混合架构 (创新方案)
**目标**: 结合GCN和GAT的优势
**方法**: GAT负责局部注意力，GCN负责全局传播

## 🔧 实验配置

### 超参数设置
```python
GAT_CONFIG = {
    'n_heads': 4,           # 注意力头数量
    'dropout': 0.1,         # GAT内部dropout
    'alpha': 0.2,           # LeakyReLU参数
    'residual': True,       # 是否使用残差连接
}

# 训练配置保持与基线一致
TRAIN_CONFIG = {
    'epochs': 40,
    'batch_size': 32,
    'lr': 1e-4,
    'patience': 8,
    'n_folds': 5,
    'seed': 2024
}
```

### 评估指标
- **主要指标**: AUC (与基线比较)
- **辅助指标**: AUPRC, MCC, F1-score
- **训练指标**: 收敛速度, 训练稳定性

## 📊 实验流程

### Step 1: 准备GAT模块
```bash
# 创建改进的GAT层
cp gat_layer_improved.py ./
```

### Step 2: 实验1 - 单层替换
```bash
# 修改 self_attention.py (只替换gcn1)
python train.py --output_path ./gat_single_output/ --num_workers 0

# 比较结果
python compare_results.py --baseline ./baseline_output/ --experiment ./gat_single_output/
```

### Step 3: 实验2 - 双层替换 (如果实验1成功)
```bash
# 修改 self_attention.py (替换gcn1和gcn2)
python train.py --output_path ./gat_double_output/ --num_workers 0
```

### Step 4: 超参数调优 (如果GAT有效)
```bash
# 测试不同的注意力头数量
for heads in 2 4 8; do
    python train.py --output_path ./gat_heads_${heads}/ --gat_heads ${heads}
done
```

## 🎯 成功标准

### 最低标准 (必须达到)
- GAT模型能正常训练收敛
- 验证集AUC不低于基线的95%

### 理想标准 (期望达到)
- 验证集AUC提升 > 2%
- 训练更稳定 (loss曲线更平滑)
- 收敛更快 (更少的epochs达到最佳性能)

### 优秀标准 (超预期)
- 验证集AUC提升 > 5%
- 在其他指标(AUPRC, MCC)上也有提升
- 模型泛化能力更强

## ⚠️ 风险控制

### 潜在问题及解决方案
1. **内存不足**: 
   - 减少注意力头数量
   - 使用梯度检查点
   - 减小batch_size

2. **训练不稳定**:
   - 调整学习率
   - 增加dropout
   - 使用梯度裁剪

3. **性能下降**:
   - 检查GAT实现
   - 调整超参数
   - 考虑混合架构

### 回退策略
如果GAT实验失败，可以考虑：
- 使用更简单的注意力机制
- 只在特定层使用GAT
- 调整GAT的架构设计

## 📈 结果分析

### 定量分析
- 性能指标对比表
- 训练曲线对比图
- 统计显著性检验

### 定性分析
- 注意力权重可视化
- 错误案例分析
- 计算复杂度分析

## 🚀 下一步计划

如果GAT实验成功：
1. 进入阶段三：特征工程优化
2. 探索更高级的注意力机制
3. 考虑多尺度图结构

如果GAT实验失败：
1. 分析失败原因
2. 尝试其他图神经网络架构
3. 重新审视问题建模方式
