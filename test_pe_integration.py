# test_pe_integration.py
# 测试PE集成是否正常工作
import os
import torch
import pandas as pd
from model import MVGNN
from utils import TaskDataset
from torch.utils.data import DataLoader

def test_pe_integration():
    """测试PE集成功能"""
    print("开始测试PE集成...")
    
    # 检查是否有PE文件
    feature_path = './feature/'
    pe_files = [f for f in os.listdir(feature_path) if f.endswith('_pe.tensor')]
    print(f"找到 {len(pe_files)} 个PE文件")
    
    if len(pe_files) == 0:
        print("警告：没有找到PE文件，请先运行PE生成脚本")
        return False
    
    # 测试加载一个PE文件
    sample_pe_file = pe_files[0]
    sample_pe_path = os.path.join(feature_path, sample_pe_file)
    pe_tensor = torch.load(sample_pe_path)
    print(f"样本PE文件 {sample_pe_file} 形状: {pe_tensor.shape}")
    
    # 测试模型初始化
    try:
        model = MVGNN(
            node_features=1024 + 14 + 20,  # ProtTrans + DSSP + BLOSUM62
            edge_features=16,
            hidden_dim=128,
            num_encoder_layers=4,
            k_neighbors=30,
            augment_eps=0.1,
            dropout=0.3
        )
        print("✓ 模型初始化成功")
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        return False
    
    # 测试数据加载
    try:
        test_df = pd.read_csv('./datasets/PRO_Test60.csv')
        ID_list = list(set(test_df['ID']))

        all_protein_data = {}
        valid_ids = []

        for pdb_id in ID_list:
            try:
                # 检查所有必需的文件是否存在
                required_files = [
                    f'./feature/{pdb_id}_X.tensor',
                    f'./feature/{pdb_id}_node_feature.tensor',
                    f'./feature/{pdb_id}_mask.tensor',
                    f'./feature/{pdb_id}_label.tensor',
                    f'./feature/{pdb_id}_adj.tensor'
                ]

                if all(os.path.exists(f) for f in required_files):
                    all_protein_data[pdb_id] = (
                        torch.load(f'./feature/{pdb_id}_X.tensor'),
                        torch.load(f'./feature/{pdb_id}_node_feature.tensor'),
                        torch.load(f'./feature/{pdb_id}_mask.tensor'),
                        torch.load(f'./feature/{pdb_id}_label.tensor'),
                        torch.load(f'./feature/{pdb_id}_adj.tensor')
                    )
                    valid_ids.append(pdb_id)

                    # 只测试前5个有效的蛋白质
                    if len(valid_ids) >= 5:
                        break

            except Exception as e:
                print(f"跳过 {pdb_id}: {e}")
                continue

        print(f"✓ 成功加载 {len(all_protein_data)} 个蛋白质数据")

        # 创建只包含有效ID的测试数据框
        valid_test_df = test_df[test_df['ID'].isin(valid_ids)].head(len(all_protein_data)).reset_index(drop=True)

        # 测试数据集
        test_dataset = TaskDataset(valid_test_df, all_protein_data, ['label'])
        test_dataloader = DataLoader(test_dataset, batch_size=2, collate_fn=test_dataset.collate_fn, shuffle=False)
        
        print("✓ 数据集创建成功")
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False
    
    # 测试前向传播
    try:
        # 检查是否有GPU可用
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()

        with torch.no_grad():
            for data in test_dataloader:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data

                # 将数据移到GPU
                protein_X = protein_X.to(device)
                protein_node_features = protein_node_features.to(device)
                protein_masks = protein_masks.to(device)
                labels = labels.to(device)
                adj = adj.to(device)
                pe = pe.to(device)

                print(f"批次形状:")
                print(f"  protein_X: {protein_X.shape}")
                print(f"  protein_node_features: {protein_node_features.shape}")
                print(f"  protein_masks: {protein_masks.shape}")
                print(f"  adj: {adj.shape}")
                print(f"  pe: {pe.shape}")

                # 前向传播
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                print(f"  outputs: {outputs.shape}")
                print("✓ 前向传播成功")
                break  # 只测试第一个批次
                
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("🎉 PE集成测试全部通过！")
    return True

if __name__ == "__main__":
    test_pe_integration()
