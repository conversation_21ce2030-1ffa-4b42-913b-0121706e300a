# unified_losses.py
# 统一的损失函数库，专为MVGNN蛋白质结合位点预测优化
# 整合了Focal Loss、Tversky Loss、Lovasz-Hinge Loss的优点

import torch
import torch.nn as nn
import torch.nn.functional as F

# ==================== 基础损失函数 ====================

class FocalLoss(nn.Module):
    """
    Focal Loss for binary classification
    解决类别不平衡问题，关注难分类样本
    """
    def __init__(self, alpha=0.25, gamma=2.0):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma

    def forward(self, logits, targets):
        targets = targets.float()
        probs = torch.sigmoid(logits)
        bce_loss = F.binary_cross_entropy_with_logits(logits, targets, reduction='none')
        p_t = (targets * probs) + ((1 - targets) * (1 - probs))
        modulating_factor = (1.0 - p_t) ** self.gamma
        alpha_weight = (targets * self.alpha) + ((1 - targets) * (1 - self.alpha))
        focal_loss = alpha_weight * modulating_factor * bce_loss
        return focal_loss.mean()

class TverskyLoss(nn.Module):
    """
    Tversky Loss for binary classification
    通过alpha和beta参数精确控制对FP和FN的惩罚
    """
    def __init__(self, alpha=0.3, beta=0.7, eps=1e-7):
        super(TverskyLoss, self).__init__()
        self.alpha = alpha  # 控制FP的惩罚
        self.beta = beta    # 控制FN的惩罚
        self.eps = eps

    def forward(self, logits, targets):
        targets = targets.float()
        probs = torch.sigmoid(logits)

        # 展平张量
        probs = probs.view(-1)
        targets = targets.view(-1)

        # 计算TP, FP, FN
        TP = (probs * targets).sum()
        FP = ((1 - targets) * probs).sum()
        FN = (targets * (1 - probs)).sum()

        # 计算Tversky Index
        tversky_index = (TP + self.eps) / (TP + self.alpha * FP + self.beta * FN + self.eps)

        return 1 - tversky_index

# ==================== Lovasz-Hinge Loss ====================

def lovasz_hinge(logits, labels, per_sample=True):
    """
    Binary Lovasz hinge loss - 直接优化IoU
    """
    if per_sample:
        if len(logits.shape) == 1:
            return lovasz_hinge_flat(logits, labels)
        else:
            losses = []
            for i in range(logits.size(0)):
                sample_loss = lovasz_hinge_flat(logits[i], labels[i])
                losses.append(sample_loss)
            return torch.stack(losses).mean()
    else:
        return lovasz_hinge_flat(logits.view(-1), labels.view(-1))

def lovasz_hinge_flat(logits, labels):
    """Binary Lovasz hinge loss for flattened tensors"""
    if len(labels) == 0:
        return logits.sum() * 0.

    labels = labels.float()
    signs = 2. * labels - 1.
    errors = 1. - logits * signs
    errors_sorted, perm = torch.sort(errors, dim=0, descending=True)
    gt_sorted = labels[perm]
    grad = lovasz_grad(gt_sorted)
    loss = torch.dot(F.relu(errors_sorted), grad)
    return loss

def lovasz_grad(gt_sorted):
    """Computes gradient of the Lovasz extension w.r.t sorted errors"""
    p = len(gt_sorted)
    gts = gt_sorted.sum()
    intersection = gts - gt_sorted.cumsum(0)
    union = gts + (1 - gt_sorted).cumsum(0)
    jaccard = 1. - intersection / union
    if p > 1:
        jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
    return jaccard

class LovaszHinge(nn.Module):
    """Lovasz-Hinge loss wrapper"""
    def __init__(self, per_sample=True):
        super(LovaszHinge, self).__init__()
        self.per_sample = per_sample

    def forward(self, logits, labels):
        return lovasz_hinge(logits, labels, per_sample=self.per_sample)

# ==================== 组合损失函数 ====================

class CombinedLoss(nn.Module):
    """
    多种损失函数的智能组合
    可以组合Focal、Tversky、Lovasz三种损失函数
    """
    def __init__(self,
                 focal_weight=0.4,
                 tversky_weight=0.4,
                 lovasz_weight=0.2,
                 focal_alpha=0.25,
                 focal_gamma=2.0,
                 tversky_alpha=0.3,
                 tversky_beta=0.7,
                 lovasz_per_sample=True):
        super(CombinedLoss, self).__init__()

        # 权重归一化
        total_weight = focal_weight + tversky_weight + lovasz_weight
        self.focal_weight = focal_weight / total_weight
        self.tversky_weight = tversky_weight / total_weight
        self.lovasz_weight = lovasz_weight / total_weight

        # 初始化各个损失函数
        self.focal = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.tversky = TverskyLoss(alpha=tversky_alpha, beta=tversky_beta)
        self.lovasz = LovaszHinge(per_sample=lovasz_per_sample)

    def forward(self, logits, labels):
        focal_loss = self.focal(logits, labels)
        tversky_loss = self.tversky(logits, labels)
        lovasz_loss = self.lovasz(logits, labels)

        combined = (self.focal_weight * focal_loss +
                   self.tversky_weight * tversky_loss +
                   self.lovasz_weight * lovasz_loss)
        return combined

class AdaptiveCombinedLoss(nn.Module):
    """
    自适应组合损失函数
    根据训练进度动态调整各损失函数的权重
    """
    def __init__(self,
                 focal_alpha=0.25,
                 focal_gamma=2.0,
                 tversky_alpha=0.3,
                 tversky_beta=0.7):
        super(AdaptiveCombinedLoss, self).__init__()

        self.focal = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.tversky = TverskyLoss(alpha=tversky_alpha, beta=tversky_beta)
        self.lovasz = LovaszHinge(per_sample=True)

        self.epoch = 0

    def set_epoch(self, epoch):
        """设置当前训练轮次，用于动态调整权重"""
        self.epoch = epoch

    def forward(self, logits, labels):
        focal_loss = self.focal(logits, labels)
        tversky_loss = self.tversky(logits, labels)
        lovasz_loss = self.lovasz(logits, labels)

        # 动态权重：早期更依赖Focal，后期更依赖Tversky和Lovasz
        if self.epoch < 10:
            # 早期：主要用Focal解决类别不平衡
            focal_w, tversky_w, lovasz_w = 0.6, 0.3, 0.1
        elif self.epoch < 25:
            # 中期：平衡使用
            focal_w, tversky_w, lovasz_w = 0.4, 0.4, 0.2
        else:
            # 后期：更关注精确的边界和形状
            focal_w, tversky_w, lovasz_w = 0.3, 0.4, 0.3

        combined = (focal_w * focal_loss +
                   tversky_w * tversky_loss +
                   lovasz_w * lovasz_loss)
        return combined

# ==================== 测试函数 ====================

def test_losses():
    """测试所有损失函数的正确性"""
    print("Testing unified loss functions...")

    # 创建测试数据
    batch_size, seq_len = 4, 100
    logits = torch.randn(batch_size, seq_len, requires_grad=True)
    labels = torch.randint(0, 2, (batch_size, seq_len)).float()

    # 测试各个损失函数
    focal = FocalLoss()
    tversky = TverskyLoss()
    lovasz = LovaszHinge()
    combined = CombinedLoss()
    adaptive = AdaptiveCombinedLoss()

    print(f"Focal Loss: {focal(logits, labels).item():.6f}")
    print(f"Tversky Loss: {tversky(logits, labels).item():.6f}")
    print(f"Lovasz Loss: {lovasz(logits, labels).item():.6f}")
    print(f"Combined Loss: {combined(logits, labels).item():.6f}")

    adaptive.set_epoch(5)
    print(f"Adaptive Loss (epoch 5): {adaptive(logits, labels).item():.6f}")

    adaptive.set_epoch(30)
    print(f"Adaptive Loss (epoch 30): {adaptive(logits, labels).item():.6f}")

    print("All tests passed!")

if __name__ == "__main__":
    test_losses()