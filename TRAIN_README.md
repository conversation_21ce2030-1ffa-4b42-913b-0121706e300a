# MVGNN-PPIS Training Guide

This document provides instructions for training the MVGNN (Multi-View Graph Neural Network) model for Protein-Protein Interaction Site (PPIS) prediction.

## Prerequisites

Before training, ensure you have:

1. Processed all protein features using the scripts in the `process_feature` directory
2. Generated all necessary tensor files in the `feature` directory
3. Prepared your training data CSV file with protein IDs and labels

## Training the Model

The training process uses 5-fold cross-validation by default. To train the model, run:

```bash
python train.py --dataset_path ./datasets/ --feature_path ./feature/ --output_path ./output/ --task PRO --num_workers 8 --seed 2024
```

### Command-line Arguments

- `--dataset_path`: Path to the directory containing dataset CSV files
- `--feature_path`: Path to the directory containing processed feature tensor files
- `--output_path`: Path to save model weights and logs
- `--task`: Task name (default: 'PRO' for protein-protein interaction site prediction)
- `--num_workers`: Number of data loading workers (default: 8)
- `--seed`: Random seed for reproducibility (default: 2024)
- `--n_folds`: Number of cross-validation folds (default: 5)

## Model Configuration

The model uses the following default configuration:

- Node features: 1038 dimensions (1024 from ProtT5 + 14 from DSSP)
- Edge features: 16 dimensions
- Hidden dimension: 128
- Number of encoder layers: 4
- K-neighbors: 30
- Augmentation epsilon: 0.1
- Dropout rate: 0.3
- Batch size: 32
- Training epochs: 30
- Early stopping patience: 8
- Focal Loss parameters: alpha=0.25, gamma=2.0

## Training Process

The training process includes:

1. 5-fold cross-validation on the training dataset
2. Data augmentation with random noise
3. Focal Loss to handle class imbalance
4. Early stopping based on validation F1 score
5. Model weights saved for each fold

## Evaluation Metrics

The model is evaluated using multiple metrics:

- Area Under ROC Curve (AUROC)
- Area Under Precision-Recall Curve (AUPRC)
- Matthews Correlation Coefficient (MCC)
- Accuracy
- Precision
- Recall
- F1 Score

## Testing the Model

After training, you can test the model using the existing `main.py` script:

```bash
python main.py --dataset_path ./datasets/ --feature_path ./feature/ --output_path ./output/ --task PRO
```

This will load the trained models from each fold and make predictions on the test dataset.
