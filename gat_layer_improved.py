import torch
import torch.nn as nn
import torch.nn.functional as F

class GATLayer(nn.Module):
    """
    改进的图注意力层 (GAT) 实现
    - 优化了内存使用
    - 添加了残差连接
    - 改进了注意力计算
    """
    def __init__(self, in_features, out_features, n_heads, dropout=0.1, alpha=0.2, concat=True):
        super(GATLayer, self).__init__()
        self.dropout = dropout
        self.in_features = in_features
        self.out_features = out_features
        self.n_heads = n_heads
        self.alpha = alpha
        self.concat = concat

        # 确保输出维度正确
        if concat:
            assert out_features * n_heads == in_features, f"concat模式下，out_features * n_heads ({out_features * n_heads}) 必须等于 in_features ({in_features})"
        
        # 每个头的输出维度
        self.head_dim = out_features
        
        # 线性变换层
        self.W = nn.Linear(in_features, n_heads * out_features, bias=False)
        
        # 注意力参数 - 为每个头分别定义
        self.a_src = nn.Parameter(torch.zeros(size=(n_heads, out_features)))
        self.a_dst = nn.Parameter(torch.zeros(size=(n_heads, out_features)))
        
        # 初始化参数
        nn.init.xavier_uniform_(self.W.weight, gain=1.414)
        nn.init.xavier_uniform_(self.a_src, gain=1.414)
        nn.init.xavier_uniform_(self.a_dst, gain=1.414)

        self.leakyrelu = nn.LeakyReLU(self.alpha)
        
        # 残差连接的投影层（如果需要）
        self.residual_proj = None
        if concat and (n_heads * out_features != in_features):
            self.residual_proj = nn.Linear(in_features, n_heads * out_features)

    def forward(self, h, adj, mask):
        """
        Args:
            h: 节点特征 [B, N, in_features]
            adj: 邻接矩阵 [B, N, N]
            mask: 节点掩码 [B, N]
        Returns:
            输出特征 [B, N, out_features * n_heads] (如果concat=True)
        """
        B, N, _ = h.shape
        
        # 1. 线性变换
        h_transformed = self.W(h)  # [B, N, n_heads * out_features]
        h_transformed = h_transformed.view(B, N, self.n_heads, self.head_dim)  # [B, N, n_heads, head_dim]
        
        # 2. 计算注意力分数 - 更高效的实现
        # 计算源节点和目标节点的注意力分量
        attn_src = torch.sum(h_transformed * self.a_src.unsqueeze(0).unsqueeze(0), dim=-1)  # [B, N, n_heads]
        attn_dst = torch.sum(h_transformed * self.a_dst.unsqueeze(0).unsqueeze(0), dim=-1)  # [B, N, n_heads]
        
        # 广播计算注意力分数
        attn_scores = attn_src.unsqueeze(2) + attn_dst.unsqueeze(1)  # [B, N, N, n_heads]
        attn_scores = self.leakyrelu(attn_scores)
        
        # 3. 应用邻接矩阵掩码
        mask_value = -1e9
        adj_mask = adj.unsqueeze(-1).expand_as(attn_scores)  # [B, N, N, n_heads]
        attn_scores = torch.where(adj_mask > 0, attn_scores, torch.tensor(mask_value, device=h.device))
        
        # 4. Softmax归一化
        attn_weights = F.softmax(attn_scores, dim=2)  # [B, N, N, n_heads]
        attn_weights = F.dropout(attn_weights, self.dropout, training=self.training)
        
        # 5. 聚合邻居特征
        h_transformed = h_transformed.permute(0, 2, 1, 3)  # [B, n_heads, N, head_dim]
        attn_weights = attn_weights.permute(0, 3, 1, 2)    # [B, n_heads, N, N]
        
        h_output = torch.matmul(attn_weights, h_transformed)  # [B, n_heads, N, head_dim]
        h_output = h_output.permute(0, 2, 1, 3)  # [B, N, n_heads, head_dim]
        
        # 6. 处理多头输出
        if self.concat:
            h_output = h_output.reshape(B, N, self.n_heads * self.head_dim)
        else:
            h_output = h_output.mean(dim=2)
        
        # 7. 残差连接
        if self.concat:
            if self.residual_proj is not None:
                residual = self.residual_proj(h)
            else:
                residual = h
            h_output = h_output + residual
        
        # 8. 应用节点掩码
        h_output = h_output * mask.unsqueeze(-1)
        
        return h_output

class MultiHeadGATLayer(nn.Module):
    """
    多头GAT层，支持更灵活的配置
    """
    def __init__(self, in_features, out_features, n_heads=4, dropout=0.1, alpha=0.2):
        super(MultiHeadGATLayer, self).__init__()
        
        self.n_heads = n_heads
        self.head_dim = out_features // n_heads
        assert out_features % n_heads == 0, "out_features必须能被n_heads整除"
        
        # 多个GAT头
        self.gat_heads = nn.ModuleList([
            GATLayer(in_features, self.head_dim, 1, dropout, alpha, concat=False)
            for _ in range(n_heads)
        ])
        
        # 输出投影
        self.output_proj = nn.Linear(out_features, out_features)
        self.layer_norm = nn.LayerNorm(out_features)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, h, adj, mask):
        # 多头并行计算
        head_outputs = []
        for gat_head in self.gat_heads:
            head_out = gat_head(h, adj, mask)
            head_outputs.append(head_out)
        
        # 拼接多头输出
        h_output = torch.cat(head_outputs, dim=-1)
        
        # 输出投影和归一化
        h_output = self.output_proj(h_output)
        h_output = self.layer_norm(h_output + h)  # 残差连接
        h_output = self.dropout(h_output)
        
        return h_output * mask.unsqueeze(-1)
