# lovasz_hinge.py
# Optimized Lovasz-Hinge loss for binary protein binding site prediction
# Specifically designed for MVGNN model architecture

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def lovasz_hinge(logits, labels, per_sample=True):
    """
    Binary Lovasz hinge loss
    Args:
        logits: [N] or [B, N] Variable, logits at each position (between -∞ and +∞)
        labels: [N] or [B, N] Tensor, binary ground truth masks (0 or 1)
        per_sample: bool, if True compute loss per sample then average
    """
    if per_sample:
        # Handle batch dimension - compute loss per sample then average
        if len(logits.shape) == 1:
            # Single sample case
            return lovasz_hinge_flat(logits, labels)
        else:
            # Batch case - compute per sample
            losses = []
            for i in range(logits.size(0)):
                sample_loss = lovasz_hinge_flat(logits[i], labels[i])
                losses.append(sample_loss)
            return torch.stack(losses).mean()
    else:
        # Flatten everything and compute single loss
        return lovasz_hinge_flat(logits.view(-1), labels.view(-1))

def lovasz_hinge_flat(logits, labels):
    """
    Binary Lovasz hinge loss for flattened tensors
    Args:
        logits: [N] Variable, logits at each position (between -∞ and +∞)
        labels: [N] Tensor, binary ground truth masks (0 or 1)
    """
    if len(labels) == 0:
        # Only void pixels, the gradients should be 0
        return logits.sum() * 0.
    
    # Convert labels to float and create signs
    labels = labels.float()
    signs = 2. * labels - 1.  # Convert 0,1 to -1,1
    
    # Compute hinge errors: max(0, 1 - logits * signs)
    errors = 1. - logits * signs
    errors_sorted, perm = torch.sort(errors, dim=0, descending=True)
    
    # Sort ground truth according to errors
    gt_sorted = labels[perm]
    
    # Compute Lovasz gradient
    grad = lovasz_grad(gt_sorted)
    
    # Compute loss: dot product of sorted errors and gradient
    # Use ReLU to ensure non-negative errors (hinge property)
    loss = torch.dot(F.relu(errors_sorted), grad)
    
    return loss

def lovasz_grad(gt_sorted):
    """
    Computes gradient of the Lovasz extension w.r.t sorted errors
    Args:
        gt_sorted: sorted ground truth labels
    """
    p = len(gt_sorted)
    gts = gt_sorted.sum()
    
    # Compute intersection and union for Jaccard index
    intersection = gts - gt_sorted.cumsum(0)
    union = gts + (1 - gt_sorted).cumsum(0)
    
    # Compute Jaccard index
    jaccard = 1. - intersection / union
    
    # Compute gradient (differences between consecutive Jaccard values)
    if p > 1:  # Handle single pixel case
        jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
    
    return jaccard

class LovaszHinge(nn.Module):
    """
    Lovasz-Hinge loss for binary classification
    Optimized for protein binding site prediction with MVGNN
    """
    def __init__(self, per_sample=True, reduction='mean'):
        super(LovaszHinge, self).__init__()
        self.per_sample = per_sample
        self.reduction = reduction

    def forward(self, logits, labels):
        """
        Forward pass
        Args:
            logits: [N] or [B, N] tensor of logits (raw model outputs)
            labels: [N] or [B, N] tensor of binary labels (0 or 1)
        Returns:
            loss: scalar tensor
        """
        # Ensure inputs are float tensors
        logits = logits.float()
        labels = labels.float()
        
        # Compute Lovasz-Hinge loss
        loss = lovasz_hinge(logits, labels, per_sample=self.per_sample)
        
        # Apply reduction if needed
        if self.reduction == 'mean' and torch.is_tensor(loss) and loss.dim() > 0:
            loss = loss.mean()
        elif self.reduction == 'sum' and torch.is_tensor(loss) and loss.dim() > 0:
            loss = loss.sum()
            
        return loss

class CombinedLoss(nn.Module):
    """
    Combined loss function: Focal Loss + Lovasz-Hinge Loss
    This combines the class imbalance handling of Focal Loss 
    with the IoU optimization of Lovasz-Hinge Loss
    """
    def __init__(self, focal_weight=0.5, focal_alpha=0.25, focal_gamma=2.0, 
                 lovasz_per_sample=True):
        super(CombinedLoss, self).__init__()
        self.focal_weight = focal_weight
        self.lovasz_weight = 1.0 - focal_weight
        
        # Import FocalLoss from your existing implementation
        from focalLoss import FocalLoss
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.lovasz_loss = LovaszHinge(per_sample=lovasz_per_sample)
    
    def forward(self, logits, labels):
        """
        Forward pass
        Args:
            logits: [N] or [B, N] tensor of logits
            labels: [N] or [B, N] tensor of binary labels
        Returns:
            combined_loss: scalar tensor
        """
        focal = self.focal_loss(logits, labels)
        lovasz = self.lovasz_loss(logits, labels)
        
        combined = self.focal_weight * focal + self.lovasz_weight * lovasz
        return combined

# Test function to verify implementation
def test_lovasz_hinge():
    """Test function to verify the Lovasz-Hinge implementation"""
    print("Testing Lovasz-Hinge Loss Implementation...")
    
    # Test case 1: Simple 1D case
    logits = torch.randn(10, requires_grad=True)
    labels = torch.randint(0, 2, (10,)).float()
    
    criterion = LovaszHinge()
    loss = criterion(logits, labels)
    
    print(f"Test 1 - 1D case: Loss = {loss.item():.6f}")
    
    # Test case 2: Batch case
    logits_batch = torch.randn(4, 20, requires_grad=True)
    labels_batch = torch.randint(0, 2, (4, 20)).float()
    
    loss_batch = criterion(logits_batch, labels_batch)
    print(f"Test 2 - Batch case: Loss = {loss_batch.item():.6f}")
    
    # Test case 3: Gradient computation
    loss_batch.backward()
    print(f"Test 3 - Gradients computed successfully: {logits_batch.grad is not None}")
    
    print("All tests passed!")

if __name__ == "__main__":
    test_lovasz_hinge()
