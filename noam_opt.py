import torch
from torch import optim
from sam import SAM

class NoamOpt:
    "Optim wrapper that implements rate."
    def __init__(self, model_size, factor, warmup, optimizer):
        self.optimizer = optimizer
        self._step = 0
        self.warmup = warmup
        self.factor = factor
        self.model_size = model_size
        self._rate = 0
        
    def step(self):
        "Update parameters and rate"
        self._step += 1
        rate = self.rate()
        for p in self.optimizer.param_groups:
            p['lr'] = rate
        self._rate = rate
        self.optimizer.step()
        
    def rate(self, step = None):
        "Implement `lrate` above"
        if step is None:
            step = self._step
        return self.factor * (self.model_size ** (-0.5) * min(step ** (-0.5), step * self.warmup ** (-1.5)))

    def zero_grad(self):
        self.optimizer.zero_grad()

class NoamOptSAM:
    """支持SAM优化器的Noam学习率调度器"""
    def __init__(self, model_size, factor, warmup, sam_optimizer):
        self.sam_optimizer = sam_optimizer
        self._step = 0
        self.warmup = warmup
        self.factor = factor
        self.model_size = model_size
        self._rate = 0

    def step(self, closure=None):
        """更新参数和学习率，支持SAM的两步更新"""
        self._step += 1
        rate = self.rate()

        # 更新SAM优化器中基础优化器的学习率
        for p in self.sam_optimizer.base_optimizer.param_groups:
            p['lr'] = rate
        self._rate = rate

        # 如果提供了closure，使用SAM的step方法
        if closure is not None:
            self.sam_optimizer.step(closure)
        else:
            # 否则使用传统的step方法（需要在外部处理SAM的两步更新）
            raise ValueError("SAM optimizer requires closure function")

    def first_step(self, zero_grad=False):
        """SAM的第一步：计算梯度并扰动参数"""
        self._step += 1
        rate = self.rate()

        # 更新学习率
        for p in self.sam_optimizer.base_optimizer.param_groups:
            p['lr'] = rate
        self._rate = rate

        # 执行SAM的第一步
        self.sam_optimizer.first_step(zero_grad)

    def second_step(self, zero_grad=False):
        """SAM的第二步：恢复参数并进行实际更新"""
        self.sam_optimizer.second_step(zero_grad)

    def rate(self, step=None):
        """实现Noam学习率调度"""
        if step is None:
            step = self._step
        return self.factor * (self.model_size ** (-0.5) * min(step ** (-0.5), step * self.warmup ** (-1.5)))

    def zero_grad(self):
        self.sam_optimizer.zero_grad()

def get_std_opt(task, parameters, d_model, batch_size, use_sam=False, sam_rho=0.05, weight_decay=0.0):
    """
    获取标准优化器，支持SAM（Sharpness-Aware Minimization）

    Args:
        task: 任务名称
        parameters: 模型参数
        d_model: 模型维度
        batch_size: 批次大小
        use_sam: 是否使用SAM优化器
        sam_rho: SAM的rho参数，控制扰动大小
        weight_decay: 权重衰减参数
    """
    train_size = {"PRO":335, "CA":1550, "MG":1729, "MN":547, "Metal":5469}
    # warmup_epoch = (5 if task != "Metal" else 2)
    warmup_epoch = (10 if task != "Metal" else 4)
    step_each_epoch = int(train_size[task] / batch_size)
    warmup = warmup_epoch * step_each_epoch
    top_lr = 0.0004
    factor = top_lr / (d_model ** (-0.5) * min(warmup ** (-0.5), warmup * warmup ** (-1.5)))

    if use_sam:
        # 使用SAM包装的Adam优化器
        base_optimizer = lambda params: torch.optim.Adam(params, lr=0, betas=(0.9, 0.98), eps=1e-9, weight_decay=weight_decay)
        sam_optimizer = SAM(parameters, base_optimizer, rho=sam_rho, adaptive=False)
        return NoamOptSAM(d_model, factor, warmup, sam_optimizer)
    else:
        # 标准Adam优化器
        return NoamOpt(
            d_model, factor, warmup, torch.optim.Adam(parameters, lr=0, betas=(0.9, 0.98), eps=1e-9, weight_decay=weight_decay)
        )
