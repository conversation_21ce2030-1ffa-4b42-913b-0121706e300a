# -*- coding: utf-8 -*-
import os
import pickle
import warnings
warnings.simplefilter('ignore')

import pandas as pd
from model import *
from utils import model_test,Seed_everything

import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--dataset_path", type=str, default='./datasets/')
parser.add_argument("--feature_path", type=str, default='./feature/')
parser.add_argument("--output_path", type=str, default='./gated_sam_rho0.05/')
parser.add_argument("--task", type=str, default='PRO') # PRO CA MG MN Metal
parser.add_argument("--num_workers", type=int, default=8)
parser.add_argument("--seed", type=int, default=2024)
parser.add_argument("--pe_type", type=str, default='rw', choices=['rw', 'lap'], help='Type of positional encoding to use (rw: Random Walk, lap: Laplacian)')

args = parser.parse_args()

seed = args.seed
Dataset_Path = args.dataset_path
Feature_Path = args.feature_path
output_root = args.output_path
task = args.task

Seed_everything(seed=seed)
model_class = MVGNN


test_df = pd.read_csv(Dataset_Path + 'PRO_Test60.csv')
ID_list = list(set(test_df['ID']))


all_protein_data = {}
for pdb_id in ID_list:
    # 根据PE类型选择相应的文件
    pe_suffix = '_pe_rw.tensor' if args.pe_type == 'rw' else '_pe_lap.tensor'
    pe_path = Feature_Path + f"{pdb_id}{pe_suffix}"

    if os.path.exists(pe_path):
        pe_tensor = torch.load(pe_path)
    else:
        # 如果PE文件不存在，创建零向量作为占位符
        temp_X = torch.load(Feature_Path + f"{pdb_id}_X.tensor")
        pe_tensor = torch.zeros(temp_X.shape[0], 32)  # 假设PE维度为32
        print(f"Warning: {args.pe_type.upper()}PE file not found for {pdb_id}, using zero tensor")

    all_protein_data[pdb_id] = (
        torch.load(Feature_Path+f"{pdb_id}_X.tensor"),
        torch.load(Feature_Path+f"{pdb_id}_node_feature.tensor"),
        torch.load(Feature_Path+f"{pdb_id}_mask.tensor"),
        torch.load(Feature_Path+f"{pdb_id}_label.tensor"),
        torch.load(Feature_Path+f"{pdb_id}_adj.tensor"),
        pe_tensor  # 添加PE张量
    )


nn_config = {
    'node_features': 1024 + 14 + 20, # ProtTrans + DSSP + BLOSUM62
    'edge_features': 16,
    'hidden_dim': 128,
    'num_encoder_layers': 4,
    'k_neighbors': 30,
    'augment_eps': 0.1,  # 与train.py保持一致
    'dropout': 0.3,       # 与train.py保持一致
    'id_name':'ID',
    'obj_max': 1,
    'epochs': 40,
    'patience': 8,
    'batch_size': 4,
    'num_samples': 335*5,
    'folds': 5,
    'seed': seed,
    'remark': task + f' binding site prediction with BLOSUM62 + {args.pe_type.upper()}PE'
}

if __name__ == '__main__':
    model_test(test_df, all_protein_data, model_class, nn_config, logit = True, output_root= output_root, args=args)
