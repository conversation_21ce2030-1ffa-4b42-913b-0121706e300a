import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import LayerNorm, Linear, ReLU, GELU, Dropout, Sequential, Identity

# 简单的DropPath实现，替代timm.layers.DropPath
class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()
        output = x.div(keep_prob) * random_tensor
        return output

class GPSInspiredGatedLayer(nn.Module):
    """
    GPS启发的门控层 - 结合GPS的门控机制和MVGNN的架构
    用于替换TransformerLayer中的复杂融合逻辑
    """
    def __init__(self, hidden_dim, heads=4, dropout=0.1, drop_path_prob=0.1):
        super(GPSInspiredGatedLayer, self).__init__()
        self.hidden_dim = hidden_dim
        self.heads = heads
        
        # GPS风格的Layer Normalization
        self.norm_node = LayerNorm(hidden_dim, eps=1e-6)
        
        # GPS风格的门控机制
        self.fc_node_1 = Linear(hidden_dim, hidden_dim * 4)
        self.fc_node_2 = Linear(hidden_dim * 2, hidden_dim)
        self.split_indices = [2 * hidden_dim, hidden_dim, hidden_dim]
        
        # 多信息流融合的门控控制器
        # 输入: [h_V_base, dh_attn, dh_gat] -> 输出: [gate_attn, gate_gat, gate_residual]
        self.gate_controller = Sequential(
            Linear(hidden_dim * 3, hidden_dim * 2),
            GELU(),
            Dropout(dropout),
            Linear(hidden_dim * 2, 3)  # 3个门控信号
        )
        
        # 自适应权重网络 - 根据图的特性动态调整
        self.adaptive_weight = Sequential(
            Linear(hidden_dim, hidden_dim // 4),
            GELU(),
            Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 激活函数和正则化
        self.act = GELU()
        self.dropout = Dropout(dropout)
        self.drop_path = DropPath(drop_path_prob) if drop_path_prob > 0. else Identity()
        
    def forward(self, h_V_base, dh_attn, dh_gat, mask_V=None):
        """
        GPS启发的门控融合
        Args:
            h_V_base: 基础节点特征 [B, N, hidden_dim]
            dh_attn: 几何注意力更新量 [B, N, hidden_dim]
            dh_gat: 拓扑GAT更新量 [B, N, hidden_dim]
            mask_V: 节点掩码 [B, N]
        """
        shortcut = h_V_base
        
        # 1. GPS风格的预处理
        h_V_norm = self.norm_node(h_V_base)
        
        # 2. GPS风格的门控分解
        g_node, i_node, x_node = torch.split(
            self.fc_node_1(h_V_norm), self.split_indices, dim=-1
        )
        
        # 3. 多信息流门控控制
        gate_input = torch.cat([h_V_base, dh_attn, dh_gat], dim=-1)
        gate_logits = self.gate_controller(gate_input)  # [B, N, 3]
        gate_weights = F.softmax(gate_logits, dim=-1)
        
        gate_attn = gate_weights[:, :, 0:1]      # [B, N, 1]
        gate_gat = gate_weights[:, :, 1:2]       # [B, N, 1]
        gate_residual = gate_weights[:, :, 2:3]  # [B, N, 1]
        
        # 4. 自适应图级别权重
        # 使用全局平均池化获得图级别表示
        if mask_V is not None:
            graph_repr = torch.sum(h_V_base * mask_V.unsqueeze(-1), dim=1) / (mask_V.sum(dim=1, keepdim=True) + 1e-8)
        else:
            graph_repr = torch.mean(h_V_base, dim=1)
        
        adaptive_w = self.adaptive_weight(graph_repr).unsqueeze(1)  # [B, 1, 1]
        
        # 5. 加权融合更新量
        weighted_update = (
            gate_attn * dh_attn + 
            gate_gat * dh_gat + 
            gate_residual * h_V_base
        )
        
        # 6. GPS风格的最终融合
        filter_gate = self.act(g_node) * torch.cat((i_node, weighted_update), dim=-1)
        final_update = self.fc_node_2(filter_gate)
        
        # 7. 自适应残差连接
        output = adaptive_w * final_update + (1 - adaptive_w) * shortcut
        
        # 8. DropPath和掩码
        output = self.drop_path(output)
        if mask_V is not None:
            output = output * mask_V.unsqueeze(-1)
            
        return output

class EnhancedGatedFusion(nn.Module):
    """
    增强的门控融合模块 - 专门用于MVGNN的TransformerLayer
    """
    def __init__(self, hidden_dim, dropout=0.1):
        super(EnhancedGatedFusion, self).__init__()
        self.hidden_dim = hidden_dim
        
        # 多层门控控制器
        self.gate_net = nn.Sequential(
            Linear(hidden_dim * 3, hidden_dim),
            LayerNorm(hidden_dim),
            GELU(),
            Dropout(dropout),
            Linear(hidden_dim, hidden_dim // 2),
            GELU(),
            Linear(hidden_dim // 2, 2),  # 输出两个门控权重
            nn.Sigmoid()
        )
        
        # 特征增强网络
        self.feature_enhancer = nn.Sequential(
            Linear(hidden_dim * 2, hidden_dim),
            LayerNorm(hidden_dim),
            GELU(),
            Dropout(dropout)
        )
        
        # 残差权重学习
        self.residual_weight = nn.Parameter(torch.ones(1))
        
    def forward(self, h_V_base, dh_attn, dh_gat):
        """
        增强的门控融合
        """
        # 1. 计算门控权重
        gate_input = torch.cat([h_V_base, dh_attn, dh_gat], dim=-1)
        gate_weights = self.gate_net(gate_input)  # [B, N, 2]
        
        gate_attn = gate_weights[:, :, 0:1]
        gate_gat = gate_weights[:, :, 1:2]
        
        # 2. 加权融合
        fused_update = gate_attn * dh_attn + gate_gat * dh_gat
        
        # 3. 特征增强
        enhanced_input = torch.cat([h_V_base, fused_update], dim=-1)
        enhanced_update = self.feature_enhancer(enhanced_input)
        
        # 4. 学习的残差连接
        output = h_V_base + self.residual_weight * enhanced_update
        
        return output
