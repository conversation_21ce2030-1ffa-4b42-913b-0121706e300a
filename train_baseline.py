#!/usr/bin/env python3
"""
阶段一：基础修正与建立新基线 (Foundation & Baseline)
训练脚本 - 用于建立修正后的GCN模型性能基准

修复内容：
1. 修复 noam_opt.py 中的学习率计算BUG
2. 修复 train.py 中的 get_std_opt 调用
3. 添加梯度裁剪以保证训练稳定
"""

import os
import sys
import argparse
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import KFold
from sklearn.metrics import roc_auc_score, average_precision_score
import numpy as np
from tqdm import tqdm
import json
from datetime import datetime

# 导入项目模块
from model import MVGNN
from utils import TaskDataset, Seed_everything
from train import train_model
from focalLoss import FocalLoss

def load_protein_data(task="PRO", max_proteins=None):
    """加载蛋白质数据"""
    print(f"正在加载 {task} 任务的数据...")
    
    # 加载训练数据
    train_df = pd.read_csv(f'./datasets/{task}_Train335.csv')
    
    if max_proteins:
        # 限制蛋白质数量用于快速测试
        ID_list = list(set(train_df['ID']))[:max_proteins]
        print(f"限制使用前 {max_proteins} 个蛋白质进行测试")
    else:
        ID_list = list(set(train_df['ID']))
    
    print(f"总共需要加载 {len(ID_list)} 个蛋白质")
    
    all_protein_data = {}
    valid_ids = []
    missing_files = []
    
    for pdb_id in tqdm(ID_list, desc="加载蛋白质数据"):
        try:
            # 检查所有必需的文件是否存在
            required_files = [
                f'./feature/{pdb_id}_X.tensor',
                f'./feature/{pdb_id}_node_feature.tensor',
                f'./feature/{pdb_id}_mask.tensor',
                f'./feature/{pdb_id}_label.tensor',
                f'./feature/{pdb_id}_adj.tensor'
            ]
            
            # 检查PE文件（可选）
            pe_files = [
                f'./feature/{pdb_id}_pe_lap.tensor',
                f'./feature/{pdb_id}_pe_rw.tensor'
            ]
            
            if all(os.path.exists(f) for f in required_files):
                # 加载基本特征
                protein_data = (
                    torch.load(f'./feature/{pdb_id}_X.tensor'),
                    torch.load(f'./feature/{pdb_id}_node_feature.tensor'),
                    torch.load(f'./feature/{pdb_id}_mask.tensor'),
                    torch.load(f'./feature/{pdb_id}_label.tensor'),
                    torch.load(f'./feature/{pdb_id}_adj.tensor')
                )
                
                # 尝试加载PE特征
                try:
                    if os.path.exists(pe_files[0]):
                        pe_lap = torch.load(pe_files[0])
                        protein_data = protein_data + (pe_lap,)
                    else:
                        # 如果没有PE特征，创建零向量
                        seq_len = protein_data[0].shape[0]
                        pe_lap = torch.zeros(seq_len, 16)  # 假设PE维度为16
                        protein_data = protein_data + (pe_lap,)
                except Exception as e:
                    print(f"警告：无法加载 {pdb_id} 的PE特征，使用零向量: {e}")
                    seq_len = protein_data[0].shape[0]
                    pe_lap = torch.zeros(seq_len, 16)
                    protein_data = protein_data + (pe_lap,)
                
                all_protein_data[pdb_id] = protein_data
                valid_ids.append(pdb_id)
            else:
                missing = [f for f in required_files if not os.path.exists(f)]
                missing_files.extend(missing)
                
        except Exception as e:
            print(f"跳过 {pdb_id}: {e}")
            continue
    
    print(f"✓ 成功加载 {len(all_protein_data)} 个蛋白质数据")
    if missing_files:
        print(f"⚠ 缺失文件数量: {len(set(missing_files))}")
    
    # 过滤训练数据，只保留有效的蛋白质
    valid_train_df = train_df[train_df['ID'].isin(valid_ids)].reset_index(drop=True)
    print(f"✓ 有效训练样本数量: {len(valid_train_df)}")
    
    return all_protein_data, valid_train_df

def evaluate_model(model, dataloader, criterion, device):
    """评估模型性能"""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for data in dataloader:
            if len(data) == 6:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj = data
                pe = None
            else:
                pdb_ids, protein_X, protein_node_features, protein_masks, labels, adj, pe = data
            
            # 移动到设备
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            labels = labels.to(device)
            adj = adj.to(device)
            if pe is not None:
                pe = pe.to(device)
            
            # 前向传播
            if pe is not None:
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
            else:
                outputs = model(protein_X, protein_node_features, protein_masks, adj)
            
            # 计算损失
            masked_outputs = torch.masked_select(outputs, protein_masks.bool())
            masked_labels = torch.masked_select(labels, protein_masks.bool())
            
            loss = criterion(masked_outputs, masked_labels)
            total_loss += loss.item()
            
            # 收集预测和标签
            all_predictions.extend(torch.sigmoid(masked_outputs).cpu().numpy())
            all_labels.extend(masked_labels.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    auc = roc_auc_score(all_labels, all_predictions)
    auprc = average_precision_score(all_labels, all_predictions)
    
    return avg_loss, auc, auprc

def train_baseline_model(args):
    """训练基线模型"""
    print("=" * 60)
    print("阶段一：基础修正与建立新基线")
    print("=" * 60)
    
    # 设置随机种子
    Seed_everything(args.seed)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    all_protein_data, train_df = load_protein_data(
        task=args.task, 
        max_proteins=args.max_proteins
    )
    
    if len(all_protein_data) == 0:
        print("❌ 没有可用的蛋白质数据，请检查feature目录")
        return
    
    # K折交叉验证
    kfold = KFold(n_splits=args.n_folds, shuffle=True, random_state=args.seed)
    fold_results = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(train_df)):
        print(f"\n{'='*20} Fold {fold+1}/{args.n_folds} {'='*20}")
        
        # 分割数据
        fold_train_df = train_df.iloc[train_idx].reset_index(drop=True)
        fold_val_df = train_df.iloc[val_idx].reset_index(drop=True)
        
        print(f"训练集大小: {len(fold_train_df)}")
        print(f"验证集大小: {len(fold_val_df)}")
        
        # 创建数据集和数据加载器
        train_dataset = TaskDataset(fold_train_df, all_protein_data, ['label'])
        val_dataset = TaskDataset(fold_val_df, all_protein_data, ['label'])
        
        train_dataloader = DataLoader(
            train_dataset, 
            batch_size=args.batch_size, 
            collate_fn=train_dataset.collate_fn, 
            shuffle=True
        )
        val_dataloader = DataLoader(
            val_dataset, 
            batch_size=args.batch_size, 
            collate_fn=val_dataset.collate_fn, 
            shuffle=False
        )
        
        # 初始化模型
        model = MVGNN(
            node_features=args.node_features,
            edge_features=args.edge_features,
            hidden_dim=args.hidden_dim,
            num_encoder_layers=args.num_encoder_layers,
            k_neighbors=args.k_neighbors,
            augment_eps=args.augment_eps,
            dropout=args.dropout
        ).to(device)
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 训练模型
        print("开始训练...")

        # 准备配置
        config = {
            'node_features': args.node_features,
            'edge_features': args.edge_features,
            'hidden_dim': args.hidden_dim,
            'num_encoder_layers': args.num_encoder_layers,
            'k_neighbors': args.k_neighbors,
            'augment_eps': args.augment_eps,
            'dropout': args.dropout,
            'epochs': args.epochs,
            'lr': args.lr,
            'weight_decay': args.weight_decay,
            'patience': args.patience,
            'use_sam': args.use_sam,
            'sam_rho': args.sam_rho,
            'batch_size': args.batch_size,
            'task': args.task,
            'num_samples': len(fold_train_df) * 2,  # 使用2倍训练样本数进行采样
            'loss_type': 'focal',  # 使用Focal Loss
            'focal_alpha': 0.25,
            'focal_gamma': 2.0
        }

        best_val_auc = train_model(
            train_df=fold_train_df,
            val_df=fold_val_df,
            protein_data=all_protein_data,
            model_class=MVGNN,
            config=config,
            fold=fold,
            output_root=f'./baseline_output_fold_{fold}/',
            args=args
        )
        
        fold_results.append(best_val_auc)
        print(f"Fold {fold+1} 最佳验证AUC: {best_val_auc:.6f}")
    
    # 计算平均结果
    mean_auc = np.mean(fold_results)
    std_auc = np.std(fold_results)
    
    print("\n" + "="*60)
    print("🎉 基线训练完成！")
    print("="*60)
    print(f"各折验证AUC: {[f'{auc:.6f}' for auc in fold_results]}")
    print(f"平均验证AUC: {mean_auc:.6f} ± {std_auc:.6f}")
    print("="*60)
    
    # 保存结果
    results = {
        'timestamp': datetime.now().isoformat(),
        'task': args.task,
        'n_folds': args.n_folds,
        'fold_results': fold_results,
        'mean_auc': mean_auc,
        'std_auc': std_auc,
        'args': vars(args)
    }
    
    os.makedirs('baseline_results', exist_ok=True)
    with open(f'baseline_results/baseline_{args.task}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"✓ 结果已保存到 baseline_results/ 目录")
    print(f"📊 新的GCN性能基线: {mean_auc:.6f}")
    
    return mean_auc, std_auc

def main():
    parser = argparse.ArgumentParser(description='训练基线GCN模型')
    
    # 数据参数
    parser.add_argument('--task', type=str, default='PRO', help='任务名称')
    parser.add_argument('--max_proteins', type=int, default=None, help='限制蛋白质数量（用于测试）')
    
    # 模型参数
    parser.add_argument('--node_features', type=int, default=1058, help='节点特征维度 (1024+14+20)')
    parser.add_argument('--edge_features', type=int, default=16, help='边特征维度')
    parser.add_argument('--hidden_dim', type=int, default=128, help='隐藏层维度')
    parser.add_argument('--num_encoder_layers', type=int, default=4, help='编码器层数')
    parser.add_argument('--k_neighbors', type=int, default=30, help='K近邻数量')
    parser.add_argument('--augment_eps', type=float, default=0.1, help='数据增强参数')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout率')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.0, help='权重衰减')
    parser.add_argument('--patience', type=int, default=10, help='早停耐心值')
    
    # 实验参数
    parser.add_argument('--n_folds', type=int, default=5, help='K折交叉验证的折数')
    parser.add_argument('--seed', type=int, default=2024, help='随机种子')
    
    # SAM优化器参数
    parser.add_argument('--use_sam', action='store_true', help='是否使用SAM优化器')
    parser.add_argument('--sam_rho', type=float, default=0.05, help='SAM的rho参数')

    # 其他参数
    parser.add_argument('--num_workers', type=int, default=0, help='数据加载器工作进程数')

    args = parser.parse_args()
    
    print("训练参数:")
    for key, value in vars(args).items():
        print(f"  {key}: {value}")
    
    # 开始训练
    train_baseline_model(args)

if __name__ == "__main__":
    main()
