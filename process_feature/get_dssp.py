import os, pickle
import numpy as np
from Bio import pairwise2
from pad_feature import parse_fasta_file

software_path = "./process_feature/Software/"


def process_dssp(dssp_file):
    aa_type = "ACDEFGHIKLMNPQRSTVWY"
    SS_type = "HBEGITSC"
    rASA_std = [115, 135, 150, 190, 210, 75, 195, 175, 200, 170,
                185, 160, 145, 180, 225, 115, 140, 155, 255, 230]

    with open(dssp_file, "r") as f:
        lines = f.readlines()

    seq = ""
    dssp_feature = []

    p = 0
    while lines[p].strip()[0] != "#":
        p += 1
    for i in range(p + 1, len(lines)):
        aa = lines[i][13]
        if aa == "!" or aa == "*":
            continue
        seq += aa
        SS = lines[i][16]
        if SS == " ":
            SS = "C"
        SS_vec = np.zeros(9) # The last dim represents "Unknown" for missing residues
        SS_vec[SS_type.find(SS)] = 1
        PHI = float(lines[i][103:109].strip())
        PSI = float(lines[i][109:115].strip())
        ACC = float(lines[i][34:38].strip())
        ASA = min(100, round(ACC / rASA_std[aa_type.find(aa)] * 100)) / 100
        dssp_feature.append(np.concatenate((np.array([PHI, PSI, ASA]), SS_vec)))

    return seq, dssp_feature


def match_dssp(seq, dssp, ref_seq):
    alignments = pairwise2.align.globalxx(ref_seq, seq)
    ref_seq = alignments[0].seqA
    seq = alignments[0].seqB

    SS_vec = np.zeros(9) # The last dim represent "Unknown" for missing residues
    SS_vec[-1] = 1
    padded_item = np.concatenate((np.array([360, 360, 0]), SS_vec))

    new_dssp = []
    for aa in seq:
        if aa == "-":
            new_dssp.append(padded_item)
        else:
            new_dssp.append(dssp.pop(0))

    matched_dssp = []
    for i in range(len(ref_seq)):
        if ref_seq[i] == "-":
            continue
        matched_dssp.append(new_dssp[i])

    return matched_dssp


def transform_dssp(dssp_feature):
    dssp_feature = np.array(dssp_feature)
    angle = dssp_feature[:,0:2]
    ASA_SS = dssp_feature[:,2:]

    radian = angle * (np.pi / 180)
    dssp_feature = np.concatenate([np.sin(radian), np.cos(radian), ASA_SS], axis = 1)

    return dssp_feature


def get_dssp(ID, ref_seq):
    os.makedirs("../dssp/", exist_ok=True)

    # 修复PDB文件路径 - 使用相对路径
    pdb_path = f"../datasets/pro_pro_pdb/{ID}.pdb"
    dssp_output = f"../dssp/{ID}.dssp"
    
    # 检查PDB文件是否存在
    if not os.path.exists(pdb_path):
        raise FileNotFoundError(f"PDB file not found: {pdb_path}")
    
    # 检查DSSP可执行文件是否存在
    dssp_exe = f"./Software/dssp-2.0.4/mkdssp"
    if not os.path.exists(dssp_exe):
        raise FileNotFoundError(f"DSSP executable not found: {dssp_exe}")
    
    # 运行DSSP命令并捕获输出
    cmd = f"{dssp_exe} -i {pdb_path} -o {dssp_output}"
    print(f"Running DSSP command: {cmd}")
    result = os.system(cmd)
    print(f"DSSP command result: {result}")
    
    # 使用subprocess获取更详细的错误信息
    import subprocess
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print(f"DSSP stdout: {result.stdout}")
        print(f"DSSP stderr: {result.stderr}")
        print(f"DSSP return code: {result.returncode}")
    except Exception as e:
        print(f"Error running DSSP: {e}")
    
    # 检查DSSP文件是否生成成功
    if not os.path.exists(dssp_output):
        raise FileNotFoundError(f"DSSP failed to generate output file: {dssp_output}")
    
    dssp_seq, dssp_matrix = process_dssp(dssp_output)
    if dssp_seq != ref_seq:
        dssp_matrix = match_dssp(dssp_seq, dssp_matrix, ref_seq)

    np.save("../dssp/" + ID, transform_dssp(dssp_matrix))
    os.system("rm ../dssp/" + ID + ".dssp")
