# -*- coding: utf-8 -*-
import pickle
import numpy as np
import torch
import os
from tqdm import tqdm
from pad_feature import parse_fasta_file
from get_dssp import get_dssp
from get_SC_position import PDBFeature
from get_SC_adj import prepare_adj
from get_T5embedding import getT5
from pad_feature import prepare_features
from get_graph_pe import generate_graph_pe



def process(dataset):
    # Create necessary directories
    os.makedirs('../T5raw/', exist_ok=True)
    os.makedirs('../T5norm/', exist_ok=True)

    #处理T5序列信息
    getT5(dataset,'../T5raw/','0')

    data = parse_fasta_file(dataset)
    Max_protrans = []
    Min_protrans = []
    for i, ID in tqdm(enumerate(data.keys())):
        raw_protrans = np.load('../T5raw/' + ID + ".npy")
        Max_protrans.append(np.max(raw_protrans, axis = 0))
        Min_protrans.append(np.min(raw_protrans, axis = 0))
        if i == len(data) - 1:
            Max_protrans = np.max(np.array(Max_protrans), axis = 0)
            Min_protrans = np.min(np.array(Min_protrans), axis = 0)
        elif i % 5000 == 0:
            Max_protrans = [np.max(np.array(Max_protrans), axis = 0)]
            Min_protrans = [np.min(np.array(Min_protrans), axis = 0)]


    for ID in tqdm(data.keys()):
        # T5
        raw_protrans = np.load('../T5raw/' + ID + ".npy")
        # print(f"Max_protrans: {Max_protrans}, Min_protrans: {Min_protrans}")
        protrans = (raw_protrans - Min_protrans) / (Max_protrans - Min_protrans)
        # torch.save(torch.tensor(protrans, dtype = torch.float), '../T5norm/' + ID + '.tensor')
        np.save('../T5norm/' + ID + '.npy', protrans)

        #dssp
        get_dssp(ID, data[ID][0])

        # SC prosition
        PDBFeature(ID, '../datasets/pro_pro_pdb', '../SC_position')

        # SC_adj
        prepare_adj(ID,869)

        # last - 添加序列信息用于BLOSUM62特征生成
        sequence = data[ID][0]  # 氨基酸序列
        labels = data[ID][1]    # 标签序列
        prepare_features(ID, labels, 869, sequence=sequence)

    # 生成图拓扑位置编码 (在所有其他特征生成完成后)
    print("开始生成图拓扑位置编码...")
    pdb_ids = list(data.keys())
    generate_graph_pe(pdb_ids, feature_path='../feature/', pe_dim=32)

if __name__ == '__main__':
    fasta_file = '../datasets/PRO_Train_335.fa'
    process(fasta_file)
    fasta_file = '../datasets/PRO_Test_60.fa'
    process(fasta_file)
