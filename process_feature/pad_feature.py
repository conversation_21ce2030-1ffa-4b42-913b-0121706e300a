import pickle
import numpy as np
import pandas as pd
import torch
import joblib
from tqdm import tqdm

Dataset_Path = './datasets/'
Feature_Path = '../feature/'

NODE_DIM =  14 + 1024 + 20  # DSSP + ProtT5 + BLOSUM62
# max_len = 882 # within train & tests

# BLOSUM62 矩阵定义
BLOSUM62_MATRIX = {
    'A': {'A': 4, 'R': -1, 'N': -2, 'D': -2, 'C': 0, 'Q': -1, 'E': -1, 'G': 0, 'H': -2, 'I': -1, 'L': -1, 'K': -1, 'M': -1, 'F': -2, 'P': -1, 'S': 1, 'T': 0, 'W': -3, 'Y': -2, 'V': 0},
    'R': {'R': 5, 'N': 0, 'D': -2, 'C': -3, 'Q': 1, 'E': 0, 'G': -2, 'H': 0, 'I': -3, 'L': -2, 'K': 2, 'M': -1, 'F': -3, 'P': -2, 'S': -1, 'T': -1, 'W': -3, 'Y': -2, 'V': -3},
    'N': {'N': 6, 'D': 1, 'C': -3, 'Q': 0, 'E': 0, 'G': 0, 'H': 1, 'I': -3, 'L': -3, 'K': 0, 'M': -2, 'F': -3, 'P': -2, 'S': 1, 'T': 0, 'W': -4, 'Y': -2, 'V': -3},
    'D': {'D': 6, 'C': -3, 'Q': 0, 'E': 2, 'G': -1, 'H': -1, 'I': -3, 'L': -4, 'K': -1, 'M': -3, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -4, 'Y': -3, 'V': -3},
    'C': {'C': 9, 'Q': -3, 'E': -4, 'G': -3, 'H': -3, 'I': -1, 'L': -1, 'K': -3, 'M': -1, 'F': -2, 'P': -3, 'S': -1, 'T': -1, 'W': -2, 'Y': -2, 'V': -1},
    'Q': {'Q': 5, 'E': 2, 'G': -2, 'H': 0, 'I': -3, 'L': -2, 'K': 1, 'M': 0, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -2, 'Y': -1, 'V': -2},
    'E': {'E': 5, 'G': -2, 'H': 0, 'I': -3, 'L': -3, 'K': 1, 'M': -2, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -3, 'Y': -2, 'V': -2},
    'G': {'G': 6, 'H': -2, 'I': -4, 'L': -4, 'K': -2, 'M': -3, 'F': -3, 'P': -2, 'S': 0, 'T': -2, 'W': -2, 'Y': -3, 'V': -3},
    'H': {'H': 8, 'I': -3, 'L': -3, 'K': -1, 'M': -2, 'F': -1, 'P': -2, 'S': -1, 'T': -2, 'W': -2, 'Y': 2, 'V': -3},
    'I': {'I': 4, 'L': 2, 'K': -3, 'M': 1, 'F': 0, 'P': -3, 'S': -2, 'T': -1, 'W': -3, 'Y': -1, 'V': 3},
    'L': {'L': 4, 'K': -2, 'M': 2, 'F': 0, 'P': -3, 'S': -2, 'T': -1, 'W': -2, 'Y': -1, 'V': 1},
    'K': {'K': 5, 'M': -1, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -3, 'Y': -2, 'V': -2},
    'M': {'M': 5, 'F': 0, 'P': -2, 'S': -1, 'T': -1, 'W': -1, 'Y': -1, 'V': 1},
    'F': {'F': 6, 'P': -4, 'S': -2, 'T': -2, 'W': 1, 'Y': 3, 'V': -1},
    'P': {'P': 7, 'S': -1, 'T': -1, 'W': -4, 'Y': -3, 'V': -2},
    'S': {'S': 4, 'T': 1, 'W': -3, 'Y': -2, 'V': -2},
    'T': {'T': 5, 'W': -2, 'Y': -2, 'V': 0},
    'W': {'W': 11, 'Y': 2, 'V': -3},
    'Y': {'Y': 7, 'V': -1},
    'V': {'V': 4}
}

# 氨基酸顺序
AMINO_ACIDS = 'ARNDCQEGHILKMFPSTWYV'

# 构建完整的对称矩阵
def build_blosum_embedding():
    """构建BLOSUM62嵌入查找表"""
    full_matrix = {}
    for acid1 in AMINO_ACIDS:
        full_matrix[acid1] = {}
        for acid2 in AMINO_ACIDS:
            if acid2 in BLOSUM62_MATRIX[acid1]:
                full_matrix[acid1][acid2] = BLOSUM62_MATRIX[acid1][acid2]
            else:
                full_matrix[acid1][acid2] = BLOSUM62_MATRIX[acid2][acid1]

    # 创建氨基酸到20维特征向量的查找表
    blosum_embedding = {}
    for acid in AMINO_ACIDS:
        blosum_embedding[acid] = np.array([full_matrix[acid][acid2] for acid2 in AMINO_ACIDS], dtype=np.float32)

    # 添加未知/填充的零向量
    blosum_embedding['X'] = np.zeros(20, dtype=np.float32)
    blosum_embedding['<pad>'] = np.zeros(20, dtype=np.float32)
    blosum_embedding['U'] = np.zeros(20, dtype=np.float32)  # 硒代半胱氨酸
    blosum_embedding['B'] = np.zeros(20, dtype=np.float32)  # 天冬酰胺或天冬氨酸
    blosum_embedding['Z'] = np.zeros(20, dtype=np.float32)  # 谷氨酰胺或谷氨酸
    blosum_embedding['O'] = np.zeros(20, dtype=np.float32)  # 吡咯赖氨酸

    return blosum_embedding

# 全局BLOSUM62嵌入表
BLOSUM_EMBEDDING = build_blosum_embedding()

def sequence_to_blosum_features(sequence):
    """将氨基酸序列转换为BLOSUM62特征矩阵"""
    # 处理序列中的非标准氨基酸
    valid_sequence = "".join([s if s in BLOSUM_EMBEDDING else 'X' for s in sequence])

    # 获取每个氨基酸的BLOSUM62向量
    blosum_features = np.array([BLOSUM_EMBEDDING[acid] for acid in valid_sequence])
    return blosum_features  # [L, 20]


def get_pdb_xyz(pdb_file):
    current_pos = -1000
    X = []
    current_aa = {} # 'N', 'CA', 'C', 'O'
    for line in pdb_file:
        if (line[0:4].strip() == "ATOM" and int(line[22:26].strip()) != current_pos) or line[0:4].strip() == "TER":
            if current_aa != {}:
                X.append(current_aa["CA"]) # X.append([current_aa["N"], current_aa["CA"], current_aa["C"], current_aa["O"]])
                current_aa = {}
            if line[0:4].strip() != "TER":
                current_pos = int(line[22:26].strip())

        if line[0:4].strip() == "ATOM":
            atom = line[13:16].strip()
            if atom in ['N', 'CA', 'C', 'O']:
                xyz = np.array([line[30:38].strip(), line[38:46].strip(), line[46:54].strip()]).astype(np.float32)
                current_aa[atom] = xyz
    return np.array(X)


def prepare_features(pdb_id, label, max_len, sequence=None):
    """
    准备蛋白质特征，包括ProtT5、DSSP和BLOSUM62特征

    Args:
        pdb_id: 蛋白质ID
        label: 标签序列
        max_len: 最大序列长度
        sequence: 氨基酸序列（用于生成BLOSUM62特征）
    """
    # 加载坐标
    with open('../SC_position/'+pdb_id+'_psepos_SC.pkl', 'rb') as file:
        X = joblib.load(file)

    # 加载ProtT5特征
    protrans = np.load(f'../T5norm/{pdb_id}.npy')  # [L, 1024]

    # 加载DSSP特征
    dssp = np.load(f'../dssp/{pdb_id}.npy')  # [L, 14]

    # 生成BLOSUM62特征
    if sequence is not None:
        # 确保序列长度与其他特征一致
        if len(sequence) != protrans.shape[0]:
            print(f"Warning: Sequence length mismatch for {pdb_id}: seq={len(sequence)}, protrans={protrans.shape[0]}")
            # 截断或填充序列以匹配特征长度
            if len(sequence) > protrans.shape[0]:
                sequence = sequence[:protrans.shape[0]]
            else:
                sequence = sequence + 'X' * (protrans.shape[0] - len(sequence))

        blosum_features = sequence_to_blosum_features(sequence)  # [L, 20]
    else:
        # 如果没有提供序列，使用零向量作为BLOSUM62特征
        print(f"Warning: No sequence provided for {pdb_id}, using zero BLOSUM62 features")
        blosum_features = np.zeros((protrans.shape[0], 20), dtype=np.float32)

    # 拼接所有节点特征: ProtT5 + DSSP + BLOSUM62
    node_features = np.hstack([protrans, dssp, blosum_features])  # [L, 1024+14+20=1058]

    print(f"Feature shapes for {pdb_id}: ProtT5={protrans.shape}, DSSP={dssp.shape}, BLOSUM62={blosum_features.shape}, Combined={node_features.shape}")


    # Padding
    padded_X = np.zeros((max_len, 3))
    padded_X[:X.shape[0]] = X
    padded_X = torch.tensor(padded_X, dtype = torch.float)

    padded_node_features = np.zeros((max_len, NODE_DIM))
    padded_node_features[:node_features.shape[0]] = node_features
    padded_node_features = torch.tensor(padded_node_features, dtype = torch.float)

    masks = np.zeros(max_len)
    masks[:X.shape[0]] = 1
    masks = torch.tensor(masks, dtype = torch.long)
    zero_pad = torch.zeros(1000, dtype=torch.long)
    extended_masks = torch.cat((zero_pad, masks), dim=0)

    if len(label)==X.shape[0]:
        padded_y = np.zeros(max_len)
        labels = np.array([int(digit) for digit in label])
        y = labels
        padded_y[:X.shape[0]] = y
        padded_y = torch.tensor(padded_y, dtype = torch.float)
    else:
        print(pdb_id)
        # 添加默认的 padded_y
        padded_y = torch.zeros(max_len, dtype=torch.float)

    # Save
    torch.save(padded_X, Feature_Path + f'/{pdb_id}_X.tensor')
    torch.save(padded_node_features, Feature_Path + f'/{pdb_id}_node_feature.tensor')
    torch.save(masks, Feature_Path + f'/{pdb_id}_mask.tensor')
    torch.save(padded_y, Feature_Path + f'/{pdb_id}_label.tensor')


def parse_fasta_file(file_path):
    protein_dict = {}
    with open(file_path, 'r') as file:
        lines = file.readlines()
    current_protein = None
    sequence = ""
    labels = ""
    for line in lines:
        line = line.strip()
        if line.startswith('>'):
            if current_protein:
                protein_dict[current_protein] = [sequence, labels]
                sequence = ""
                labels = ""
            current_protein = line[1:]  # 去掉 '>' 符号
        elif current_protein:
            if not sequence:
                sequence = line
            else:
                labels = line
    if current_protein:
        protein_dict[current_protein] = [sequence, labels]

    return protein_dict




if __name__ == '__main__':
    proteindata = parse_fasta_file('./datasets/Test_315.fa')  # 使用函数

    for ID in proteindata.keys():
        # if ID !='4cej1_B':
        sequence = proteindata[ID][0]  # 氨基酸序列
        labels = proteindata[ID][1]    # 标签序列
        prepare_features(ID, labels, 869, sequence=sequence)
