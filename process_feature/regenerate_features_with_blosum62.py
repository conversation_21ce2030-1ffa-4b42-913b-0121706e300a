#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重新生成包含BLOSUM62特征的节点特征文件
这个脚本会读取现有的ProtT5和DSSP特征，结合序列信息生成BLOSUM62特征，
然后重新保存包含所有三种特征的节点特征文件。
"""

import os
import sys
import numpy as np
import torch
from tqdm import tqdm
import joblib

# 添加当前目录到路径
sys.path.append('.')
from pad_feature import sequence_to_blosum_features, parse_fasta_file

def regenerate_features_with_blosum62(fasta_file, feature_dir='../feature/', max_len=869):
    """
    重新生成包含BLOSUM62特征的节点特征文件
    
    Args:
        fasta_file: FASTA文件路径
        feature_dir: 特征文件目录
        max_len: 最大序列长度
    """
    print(f"Processing FASTA file: {fasta_file}")
    
    # 解析FASTA文件获取序列信息
    protein_data = parse_fasta_file(fasta_file)
    print(f"Found {len(protein_data)} proteins")
    
    # 确保输出目录存在
    os.makedirs(feature_dir, exist_ok=True)
    
    success_count = 0
    error_count = 0
    
    for pdb_id in tqdm(protein_data.keys(), desc="Regenerating features"):
        try:
            sequence = protein_data[pdb_id][0]  # 氨基酸序列
            
            # 检查现有特征文件是否存在
            protrans_file = f'../T5norm/{pdb_id}.npy'
            dssp_file = f'../dssp/{pdb_id}.npy'
            
            if not os.path.exists(protrans_file):
                print(f"Warning: ProtT5 file not found for {pdb_id}: {protrans_file}")
                error_count += 1
                continue
                
            if not os.path.exists(dssp_file):
                print(f"Warning: DSSP file not found for {pdb_id}: {dssp_file}")
                error_count += 1
                continue
            
            # 加载现有特征
            protrans = np.load(protrans_file)  # [L, 1024]
            dssp = np.load(dssp_file)  # [L, 14]
            
            # 确保序列长度与特征长度一致
            feature_length = protrans.shape[0]
            if len(sequence) != feature_length:
                print(f"Warning: Sequence length mismatch for {pdb_id}: seq={len(sequence)}, features={feature_length}")
                # 调整序列长度以匹配特征
                if len(sequence) > feature_length:
                    sequence = sequence[:feature_length]
                else:
                    sequence = sequence + 'X' * (feature_length - len(sequence))
            
            # 生成BLOSUM62特征
            blosum_features = sequence_to_blosum_features(sequence)  # [L, 20]
            
            # 验证特征维度
            assert protrans.shape[0] == dssp.shape[0] == blosum_features.shape[0], \
                f"Feature length mismatch for {pdb_id}: ProtT5={protrans.shape[0]}, DSSP={dssp.shape[0]}, BLOSUM62={blosum_features.shape[0]}"
            
            # 拼接所有特征
            combined_features = np.hstack([protrans, dssp, blosum_features])  # [L, 1058]
            
            # 填充到最大长度
            padded_features = np.zeros((max_len, combined_features.shape[1]), dtype=np.float32)
            padded_features[:combined_features.shape[0]] = combined_features
            
            # 转换为张量并保存
            feature_tensor = torch.tensor(padded_features, dtype=torch.float32)
            output_file = os.path.join(feature_dir, f'{pdb_id}_node_feature.tensor')
            torch.save(feature_tensor, output_file)
            
            success_count += 1
            
            if success_count % 50 == 0:
                print(f"Processed {success_count} proteins successfully")
                
        except Exception as e:
            print(f"Error processing {pdb_id}: {str(e)}")
            error_count += 1
            continue
    
    print(f"\nFeature regeneration completed!")
    print(f"Successfully processed: {success_count} proteins")
    print(f"Errors encountered: {error_count} proteins")
    print(f"New feature dimension: 1058 (ProtT5: 1024 + DSSP: 14 + BLOSUM62: 20)")

def verify_feature_dimensions(feature_dir='../feature/', sample_size=5):
    """验证生成的特征文件维度是否正确"""
    print(f"\nVerifying feature dimensions...")
    
    feature_files = [f for f in os.listdir(feature_dir) if f.endswith('_node_feature.tensor')]
    
    if not feature_files:
        print("No feature files found!")
        return
    
    # 随机选择几个文件进行验证
    import random
    sample_files = random.sample(feature_files, min(sample_size, len(feature_files)))
    
    for file_name in sample_files:
        file_path = os.path.join(feature_dir, file_name)
        try:
            features = torch.load(file_path)
            print(f"{file_name}: shape = {features.shape}")
            
            # 验证维度
            if features.shape[1] == 1058:
                print(f"  ✓ Correct dimension (1058)")
            else:
                print(f"  ✗ Incorrect dimension (expected 1058, got {features.shape[1]})")
                
        except Exception as e:
            print(f"  ✗ Error loading {file_name}: {str(e)}")

if __name__ == '__main__':
    # 处理训练集
    print("=" * 60)
    print("Regenerating features for training set...")
    regenerate_features_with_blosum62('../datasets/PRO_Train_335.fa')
    
    # 处理测试集
    print("\n" + "=" * 60)
    print("Regenerating features for test set...")
    regenerate_features_with_blosum62('../datasets/PRO_Test_60.fa')
    
    # 验证生成的特征
    verify_feature_dimensions()
    
    print("\n" + "=" * 60)
    print("All done! You can now train the model with BLOSUM62 features.")
    print("The node_features dimension has been updated to 1058 in the configuration.")
