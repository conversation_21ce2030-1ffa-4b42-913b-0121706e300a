# get_graph_pe.py
# 图拓扑位置编码生成脚本
import os
import torch
import torch_geometric.transforms as T
from torch_geometric.data import Data
from tqdm import tqdm

def generate_graph_pe(pdb_ids, feature_path='../feature/', pe_dim=32):
    """
    为给定的蛋白质ID列表生成图拓扑位置编码
    
    Args:
        pdb_ids: 蛋白质ID列表
        feature_path: 特征文件存储路径
        pe_dim: 位置编码维度
    """
    print(f"开始为 {len(pdb_ids)} 个蛋白质生成维度为 {pe_dim} 的图拓扑位置编码...")
    
    # 初始化PE变换器
    pe_transform = T.AddRandomWalkPE(walk_length=pe_dim, attr_name='pe')
    
    success_count = 0
    error_count = 0
    
    for pdb_id in tqdm(pdb_ids, desc="正在处理PE"):
        adj_path = os.path.join(feature_path, f"{pdb_id}_adj.tensor")
        pe_path = os.path.join(feature_path, f"{pdb_id}_pe.tensor")
        
        # 如果PE文件已存在，跳过
        if os.path.exists(pe_path):
            success_count += 1
            continue
        
        if not os.path.exists(adj_path):
            print(f"警告：找不到 {pdb_id} 的邻接矩阵文件，已跳过。")
            error_count += 1
            continue
        
        try:
            # 1. 加载邻接矩阵
            adj_matrix = torch.load(adj_path)
            num_nodes = adj_matrix.shape[0]
            
            # 2. 从邻接矩阵创建torch_geometric所需的edge_index
            # 使用阈值来确定边的存在（因为邻接矩阵可能是连续值）
            threshold = 0.01  # 可以根据需要调整
            adj_binary = (adj_matrix > threshold).float()
            edge_index = adj_binary.to_sparse().indices()
            
            # 3. 创建一个临时的、只包含图结构的Data对象
            temp_graph = Data(edge_index=edge_index, num_nodes=num_nodes)
            
            # 4. 应用变换，计算PE
            graph_with_pe = pe_transform(temp_graph)
            
            # 5. 提取PE张量并保存
            pe_tensor = graph_with_pe.pe  # 形状为 [num_nodes, pe_dim]
            torch.save(pe_tensor, pe_path)
            success_count += 1
            
        except Exception as e:
            print(f"处理 {pdb_id} 时出错: {str(e)}")
            error_count += 1
            continue
    
    print(f"PE特征生成完成！成功: {success_count}, 失败: {error_count}")
    return success_count, error_count

def main():
    """主函数，用于独立运行此脚本"""
    # 配置参数
    PE_DIM = 32 
    FEATURE_PATH = '../feature/' 
    PDB_ID_LIST_FILE = '../protein_id_list.txt' 
    
    # 读取蛋白质ID列表
    with open(PDB_ID_LIST_FILE, 'r') as f:
        pdb_ids = [line.strip() for line in f.readlines()]
    
    # 生成PE特征
    generate_graph_pe(pdb_ids, FEATURE_PATH, PE_DIM)

if __name__ == "__main__":
    main()
