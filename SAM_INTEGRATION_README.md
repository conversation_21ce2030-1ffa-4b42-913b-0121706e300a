# SAM优化器集成指南

## 概述

本文档描述了如何将SAM（Sharpness-Aware Minimization）优化器集成到MVGNN-PPIS模型中。SAM是一种先进的优化技术，通过寻找平坦的损失最小值来提升模型的泛化能力。

## SAM优化器原理

### 什么是SAM？
SAM（Sharpness-Aware Minimization）不是直接寻找损失函数的最低点，而是寻找一个较为平坦（flat）的损失区域。这种方法基于以下观察：
- **尖锐的最小值**：虽然训练损失低，但泛化能力差
- **平坦的最小值**：训练损失可能稍高，但泛化能力强

### 工作原理
SAM使用两步更新过程：
1. **第一步（扰动）**：在当前参数附近添加扰动，"爬到"损失的局部最大值
2. **第二步（更新）**：在扰动位置计算梯度，进行实际的参数更新

数学表示：
```
w' = w + ε * ∇w L(w) / ||∇w L(w)||  # 扰动参数
w_new = w - lr * ∇w' L(w')          # 在扰动位置更新
```

## 集成实现

### 1. 核心文件修改

#### `sam.py`
- 实现了SAM优化器类
- 支持自适应和非自适应模式
- 提供`first_step()`和`second_step()`方法

#### `noam_opt.py`
- 添加了`NoamOptSAM`类，支持SAM的学习率调度
- 修改了`get_std_opt()`函数，支持SAM参数

#### `train.py`
- 修改训练循环以支持SAM的两步更新
- 添加了SAM相关的命令行参数
- 实现了closure函数用于SAM优化

### 2. 关键参数

#### `rho` (扰动半径)
- **默认值**: 0.05
- **作用**: 控制参数扰动的大小
- **调优建议**:
  - 较小值(0.01-0.03): 保守的扰动，适合稳定训练
  - 中等值(0.05-0.1): 平衡性能和稳定性
  - 较大值(0.1+): 更强的正则化，可能影响收敛

#### `adaptive`
- **默认值**: False
- **作用**: 是否使用自适应扰动
- **说明**: 自适应模式根据参数大小调整扰动

## 使用方法

### 方法1: 使用修改后的train.py

```bash
# 启用SAM优化器
python train.py --use_sam --sam_rho 0.05

# 使用不同的rho值
python train.py --use_sam --sam_rho 0.1

# 训练特定fold
python train.py --use_sam --sam_rho 0.05 --n_folds 1
```

### 方法2: 使用专用的SAM训练脚本

```bash
# 使用默认参数训练
python train_with_sam.py

# 自定义参数
python train_with_sam.py --sam_rho 0.08 --batch_size 20 --epochs 25

# 训练特定fold
python train_with_sam.py --fold 0 --sam_rho 0.05

# 使用自适应SAM
python train_with_sam.py --adaptive_sam --sam_rho 0.1
```

## 性能对比

### 预期改进效果

| 指标 | 标准优化器 | SAM优化器 | 改进幅度 |
|------|------------|-----------|----------|
| 验证AUC | 0.825 | 0.835-0.845 | +1-2% |
| 测试AUC | 0.820 | 0.830-0.840 | +1-2% |
| 泛化差距 | 0.005 | 0.002-0.003 | -40-60% |
| 训练稳定性 | 中等 | 高 | 显著提升 |

### 训练特点

#### 优势
1. **更好的泛化能力**: 在测试集上表现更稳定
2. **减少过拟合**: 找到更平坦的最小值
3. **训练稳定性**: 减少训练过程中的震荡
4. **鲁棒性**: 对超参数变化不敏感

#### 代价
1. **训练时间**: 增加约80-100%（每步需要两次前向传播）
2. **内存占用**: 略有增加（需要保存扰动前的参数）
3. **实现复杂度**: 需要修改训练循环

## 超参数调优建议

### rho参数调优
```bash
# 测试不同的rho值
for rho in 0.01 0.03 0.05 0.08 0.1; do
    python train_with_sam.py --sam_rho $rho --fold 0 --epochs 10
done
```

### 批大小调整
SAM通常在较小的批大小下表现更好：
- **推荐**: 16-32
- **原因**: 小批量提供更多的梯度噪声，与SAM的平坦化效果协同

### 学习率调整
SAM可能需要稍微调整学习率：
- **建议**: 保持原有学习率或略微降低(0.8-1.0倍)
- **原因**: SAM本身提供了隐式的正则化

## 实验建议

### 快速验证
```bash
# 快速测试SAM效果（单fold，少量epoch）
python train_with_sam.py --fold 0 --epochs 10 --sam_rho 0.05
```

### 完整对比实验
```bash
# 标准训练
python train.py --n_folds 5

# SAM训练
python train.py --use_sam --sam_rho 0.05 --n_folds 5
```

### 参数搜索
```bash
# 搜索最佳rho值
for rho in 0.03 0.05 0.08; do
    python train_with_sam.py --sam_rho $rho --fold 0
done
```

## 故障排除

### 常见问题

1. **训练变慢**
   - 正常现象，SAM需要两次前向传播
   - 可以减少批大小或epoch数

2. **内存不足**
   - 减少批大小
   - 使用梯度累积

3. **收敛困难**
   - 降低rho值
   - 检查学习率设置

4. **性能没有提升**
   - 尝试不同的rho值
   - 确保训练足够的epoch
   - 检查数据集是否适合SAM

### 调试技巧

```python
# 检查SAM是否正确工作
print(f"Using SAM: {config.get('use_sam', False)}")
print(f"SAM rho: {config.get('sam_rho', 'N/A')}")

# 监控梯度范数
grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), float('inf'))
print(f"Gradient norm: {grad_norm}")
```

## 总结

SAM优化器是一个强大的工具，能够显著提升模型的泛化能力。虽然训练时间会增加，但通常能够获得更好的测试性能和更稳定的训练过程。

### 推荐配置
- **rho**: 0.05（平衡性能和稳定性）
- **batch_size**: 24-32
- **adaptive**: False（除非有特殊需求）

### 适用场景
- 需要更好泛化能力的任务
- 训练数据有限的情况
- 对模型鲁棒性要求高的应用

SAM是一个即插即用的改进，强烈建议在重要的模型训练中尝试使用！
